<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- خلفية متدرجة شبه دائرية -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient 
                android:startColor="#667EEA"
                android:centerColor="#764BA2"
                android:endColor="#6366F1"
                android:type="linear"
                android:angle="135" />
        </aapt:attr>
    </path>
    
    <!-- شكل شبه دائري -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:pathData="M20,20 L88,20 Q96,20 96,28 L96,80 Q96,88 88,88 L28,88 Q20,88 20,80 Z"/>
        
    <!-- ظل داخلي -->
    <path
        android:fillColor="#000000"
        android:fillAlpha="0.05"
        android:pathData="M24,24 L84,24 Q92,24 92,32 L92,76 Q92,84 84,84 L32,84 Q24,84 24,76 Z"/>
</vector>
