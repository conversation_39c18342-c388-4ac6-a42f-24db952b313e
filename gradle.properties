org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m
android.useAndroidX=true
android.enableJetifier=true

# Kotlin daemon settings
kotlin.daemon.jvmargs=-Xmx1G -XX:MaxMetaspaceSize=512m
kotlin.incremental=false
kotlin.compiler.execution.strategy=in-process
kotlin.daemon.useFallbackStrategy=true

# Gradle performance
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configureondemand=false

# Fix for Gradle lock issues
org.gradle.daemon=false
org.gradle.workers.max=1

# Additional stability settings
org.gradle.unsafe.configuration-cache=false
org.gradle.unsafe.configuration-cache-problems=warn
