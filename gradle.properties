# Gradle JVM settings
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -Dfile.encoding=UTF-8

# Android settings
android.useAndroidX=true
android.enableJetifier=true
android.suppressUnsupportedCompileSdk=34,35

# Kotlin settings - disable daemon to avoid connection issues
kotlin.incremental=false
kotlin.compiler.execution.strategy=in-process
kotlin.daemon.useFallbackStrategy=true

# Gradle performance (disabled for stability)
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configureondemand=false
org.gradle.daemon=false
